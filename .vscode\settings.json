{
  "editor.codeActionsOnSave": {
    "source.organizeImports": "explicit"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode",
    "editor.wordWrapColumn": 180
  },
  "[typescript]": {
    "editor.defaultFormatter": "vscode.typescript-language-features",
    "editor.wordWrapColumn": 180
  },
  "[html]": {
    "editor.defaultFormatter": "HookyQR.beautify",
    "editor.wordWrapColumn": 180
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "css.validate": false,
  "scss.validate": false,
  "sonarlint.connectedMode.project": {
    "connectionId": "SKEYE",
    "projectKey": "Skeye_Client-develop"
  },
  "codium.codeCompletion.enable": true,
  "cSpell.words": [
    "skeye"
  ],
}
