import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { AppToasterService, CommonService as CommonSharedService } from '@core/services';
import { BaseComponent } from '@core/utils';
import { BehaviorSubject, Subject, debounceTime, takeUntil } from 'rxjs';
import { HistoryModuleActions, HistoryModuleName, HistorySearchPlaceholder } from '../../constants/app.constants';
import { MESSAGES } from '../../constants/messages.constants';
import { DataType, FilterValue, OperatorType, TreeOperatorType, ViewMode } from '../../models';
import { HistoryListFilter, HistoryListItem } from '../../models/recent-activity';
import { RecentActivityService } from './recent-activity.service';

@Component({
  selector: 'app-recent-activity',
  templateUrl: './recent-activity.component.html',
  styleUrls: ['./recent-activity.component.scss'],
})
export class RecentActivityComponent extends BaseComponent implements OnInit {
  @Output() onClose = new EventEmitter<void>();
  @Input() moduleName!: string;
  @Input() modulesName!: string[];
  globalSearch = new Subject<FilterValue[]>();
  historyList: HistoryListItem[] = [];
  filterParams: HistoryListFilter = new HistoryListFilter();
  isLoading = false;
  pageNumber = 0;
  totalPages!: number;
  searchPlaceholder!: string;
  disableInfiniteScroll = false;
  isScrolling$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
  constructor(
    private readonly historyService: RecentActivityService,
    private readonly cdf: ChangeDetectorRef,
    private readonly router: Router,
    private readonly commonSharedService: CommonSharedService,
    private readonly toasterService: AppToasterService,
  ) {
    super();
  }

  ngOnInit(): void {
    this.setSearchPlaceholder();
    this.setFilterValues();
    this.getHistory(true);
    this.displaySearchResult();
  }

  getHistory(showLoader = false): void {
    this.isLoading = showLoader;

    // Debug: Log the filter params being sent to backend
    console.log('Filter params being sent:', JSON.stringify(this.filterParams, null, 2));

    this.historyService.getListWithFiltersWithPagination<HistoryListFilter, HistoryListItem>(this.filterParams, this.pageNumber + 1, 20).pipe(takeUntil(this.destroy$)).subscribe({
      next: (res) => {
        this.historyList = [...this.historyList, ...res.content];
        this.isScrolling$.next(false);
        this.totalPages = res.totalPages - 1;
        this.disableInfiniteScroll = res.totalPages <= this.pageNumber;
        this.isLoading = false;
        this.cdf.detectChanges();
      },
      error: () => {
        this.isLoading = false;
        this.isScrolling$.next(false);
        this.cdf.detectChanges();
      }
    })
  }

  get searchParam(): FilterValue {
    return {
      dataType: DataType.STRING,
      key: 'displayName',
      operator: OperatorType.LIKE,
      value: ''
    }
  }

  searchHistory(event: any): void {
    this.isLoading = true;
    if (event.value === "") {
      this.removeDisplayNameFilter();
    } else {
      this.updateDisplayNameFilter(event.value);
    }
    this.globalSearch.next(this.filterParams.values);
  }

  private removeDisplayNameFilter(): void {
    const displayNameFilterIndex = this.filterParams.values.findIndex((value) => value.key === 'displayName');
    if (displayNameFilterIndex !== -1) {
      this.filterParams.values.splice(displayNameFilterIndex, 1);
    }
  }

  private updateDisplayNameFilter(value: string): void {
    const displayNameFilter = this.filterParams.values.find((value) => value.key === 'displayName');
    if (displayNameFilter) {
      displayNameFilter.value = value;
    } else {
      const searchDisplayNameFilter = this.searchParam;
      searchDisplayNameFilter.value = value;
      this.filterParams.values.push(searchDisplayNameFilter);
    }
  }

  displaySearchResult(): void {
    this.globalSearch.pipe(debounceTime(1000)).subscribe(() => {
      this.historyList = [];
      this.pageNumber = 0;
      this.setNestedTreeOperator();
      this.getHistory();
    });
  }

  private setNestedTreeOperator(): void {
    if (this.modulesName && this.modulesName.length > 0) {
      // For multiple modules, use nested structure with AND at main level: (modules OR modules) AND search
      const hasSearchFilter = this.filterParams.values.some(value => value.key === 'displayName');

      if (hasSearchFilter) {
        // Create nested structure: left (modules OR), right (search), main (AND)
        this.createNestedFilterStructure();
      } else {
        // No search, just OR the modules
        this.filterParams.treeOperator = TreeOperatorType.OR;
      }
    } else {
      // Single module case
      this.filterParams.treeOperator = this.filterParams.values.length > 1 ? TreeOperatorType.AND : TreeOperatorType.NOOP;
    }
  }

  private createNestedFilterStructure(): void {
    const searchFilter = this.filterParams.values.find(value => value.key === 'displayName');
    const moduleFilters = this.filterParams.values.filter(value => value.key === 'module');

    if (searchFilter && moduleFilters.length > 0) {
      // Create left/right structure: (modules OR modules) AND search
      // Left: modules with OR operator
      (this.filterParams as any).left = {
        treeOperator: TreeOperatorType.OR,
        values: moduleFilters
      };

      // Right: search with NOOP operator
      (this.filterParams as any).right = {
        treeOperator: TreeOperatorType.NOOP,
        values: [searchFilter]
      };

      // Set main tree operator to AND
      this.filterParams.treeOperator = TreeOperatorType.AND;

      // Clear the flat values since we're using nested structure
      this.filterParams.values = [];
    }
  }

  closePopup(): void {
    this.onClose.emit();
  }

  onScroll(): void {
    if (!this.disableInfiniteScroll) {
      this.isScrolling$.next(true);
      this.pageNumber++;
      this.getHistory();
    }
  }

  getAction(str: string): string {
    const actionMap: Record<string, string> = {
      [HistoryModuleActions.CREATE]: 'created',
      [HistoryModuleActions.UPDATE]: 'updated',
      [HistoryModuleActions.DELETE]: 'deleted',
      [HistoryModuleActions.ARCHIVED]: 'archived',
      [HistoryModuleActions.UNARCHIVED]: 'unarchived',
      [HistoryModuleActions.MOVED]: 'moved',
    };
    return (actionMap as Record<string, string>)[str] || '';
  }

  setFilterValues(): void {
    this.filterParams.orderBy = [{ ascending: this.paginationConfig.ascending, field: this.paginationConfig.predicate }];
    this.filterParams.orderBy[0].ascending = false;
    if (this.modulesName) {
      this.filterParams.treeOperator = TreeOperatorType.OR;
      this.filterParams.values = this.modulesName.map((module) => ({
        dataType: DataType.ENUM,
        key: 'module',
        operator: OperatorType.EQUAL,
        value: module,
        enumName: 'ModuleType',
      }));
    } else {
      this.filterParams.treeOperator = TreeOperatorType.NOOP;
      this.filterParams.values = [{
        dataType: DataType.ENUM,
        key: 'module',
        operator: OperatorType.EQUAL,
        value: this.moduleName,
        enumName: 'ModuleType',
      }]
    }
  }

  trackByFunction(_index: number, item: HistoryListItem): number {
    return item.id;
  }

  setSearchPlaceholder(): void {
    const placeholderMap = {
      [HistoryModuleName.CUSTOMER_LEAD]: HistorySearchPlaceholder.LEAD_ID,
      [HistoryModuleName.CRM_CONTACT]: HistorySearchPlaceholder.CONTACT_NAME,
      [HistoryModuleName.TASK_SALES]: HistorySearchPlaceholder.TASK_ID,
      [HistoryModuleName.INCOMING_TRUCK]: HistorySearchPlaceholder.VIN_NUMBER,
      [HistoryModuleName.DRIVER_SCHEDULE]: HistorySearchPlaceholder.ITEM_NAME,
      [HistoryModuleName.INVENTORY]: HistorySearchPlaceholder.STOCK_NUMBER,
      [HistoryModuleName.PIPELINE_SOLD]: HistorySearchPlaceholder.STOCK_NUMBER,
      [HistoryModuleName.PIPELINE_STOCK]: HistorySearchPlaceholder.STOCK_NUMBER,
      [HistoryModuleName.SHOPS]: HistorySearchPlaceholder.TASK_ID,
      [HistoryModuleName.USERS]: HistorySearchPlaceholder.NAME,
      [HistoryModuleName.DEALERS]: HistorySearchPlaceholder.NAME,
      [HistoryModuleName.VENDORS]: HistorySearchPlaceholder.NAME,
      [HistoryModuleName.SUPPLIERS]: HistorySearchPlaceholder.NAME,
      [HistoryModuleName.PIPELINE_CONFIG]: HistorySearchPlaceholder.TITLE,
      [HistoryModuleName.SHOP]: HistorySearchPlaceholder.NAME,
      [HistoryModuleName.ROLES_AND_PERMISSION]: HistorySearchPlaceholder.NAME,
      [HistoryModuleName.ADVERTISING]: HistorySearchPlaceholder.NAME,
      [HistoryModuleName.INTERNET_GROUPS]: HistorySearchPlaceholder.NAME,
      [HistoryModuleName.REMINDERS]: HistorySearchPlaceholder.CONTACT_NAME,
      [HistoryModuleName.TASK_COMMENT]: HistorySearchPlaceholder.TASK_ID,
      [HistoryModuleName.QUOTE]: HistorySearchPlaceholder.LEAD_ID,
    };
    if (this.modulesName) {
      this.modulesName.forEach((module) => {
        this.searchPlaceholder = placeholderMap[module] ?? '';
      });
    } else {
      this.searchPlaceholder = placeholderMap[this.moduleName] ?? ''
    }
  }

  onClickHistory(history: HistoryListItem): void {
    if (!history.eventDeleted) {
      const returnUrl = this.router.routerState.snapshot.url;
      switch (history.module) {
        case HistoryModuleName.CUSTOMER_LEAD:
        case HistoryModuleName.QUOTE:
          this.redirectTo(`${this.path.crm.root}/${this.path.crm.crmCustomer.root}?id=${history.eventId}&returnUrl=${returnUrl}`);
          break;
        case HistoryModuleName.CRM_CONTACT:
        case HistoryModuleName.REMINDERS:
          this.redirectTo(`${this.path.crm.root}/${this.path.crm.crmContact.root}?contactId=${history.eventId}&returnUrl=${returnUrl}`);
          break;
        case HistoryModuleName.TASK_SALES:
        case HistoryModuleName.TASK_COMMENT:
          this.redirectTo(`${this.path.crm.root}/${this.path.crm.crmTask.root}?id=${history.eventId}&mode=1`);
          break;
        case HistoryModuleName.INCOMING_TRUCK:
          this.handleIncomingTruckRedirect(history, returnUrl);
          break;
        case HistoryModuleName.DRIVER_SCHEDULE:
          this.redirectTo(`${this.path.transport.root}/${this.path.transport.driverScheduleBoard.root}?id=${history.eventId}&returnUrl=${returnUrl}`)
          break;
        case HistoryModuleName.INVENTORY:
          this.redirectTo(`${this.path.inventory.root}?id=${history.eventId}&returnUrl=${returnUrl}`);
          break;
        case HistoryModuleName.PIPELINE_SOLD:
          this.redirectTo(`${this.path.pipeline.root}/${this.path.pipeline.soldTruckBoard.root}?id=${history.eventId}&returnUrl=${returnUrl}`);
          break;
        case HistoryModuleName.PIPELINE_STOCK:
          this.redirectTo(`${this.path.pipeline.root}/${this.path.pipeline.stockTruckBoard.root}?id=${history.eventId}&returnUrl=${returnUrl}`);
          break;
        case HistoryModuleName.SHOPS:
          this.redirectTo(`${this.path.shops.root}?id=${history.eventId}?mode=${ViewMode.READ}&returnUrl=${returnUrl}`);
          break;
        case HistoryModuleName.USERS:
          this.redirectTo(`${this.path.administration.root}/${this.path.administration.users.root}?id=${history.eventId}&returnUrl=${returnUrl}`);
          break;
        case HistoryModuleName.DEALERS:
          this.redirectTo(`${this.path.administration.root}/${this.path.administration.dealers.root}?id=${history.eventId}&returnUrl=${returnUrl}`);
          break;
        case HistoryModuleName.VENDORS:
          this.redirectTo(`${this.path.administration.root}/${this.path.administration.vendors.root}?id=${history.eventId}&returnUrl=${returnUrl}`);
          break;
        case HistoryModuleName.SUPPLIERS:
          this.redirectTo(`${this.path.administration.root}/${this.path.administration.suppliers.root}?id=${history.eventId}&returnUrl=${returnUrl}`);
          break;
        case HistoryModuleName.PIPELINE_CONFIG:
          this.redirectTo(`${this.path.administration.root}/${this.path.administration.pipelineConfig.root}?id=${history.eventId}&returnUrl=${returnUrl}`);
          break;
        case HistoryModuleName.SHOP:
          this.redirectTo(`${this.path.administration.root}/${this.path.administration.shops}?id=${history.eventId}&returnUrl=${returnUrl}`);
          break;
        case HistoryModuleName.ROLES_AND_PERMISSION:
          this.redirectTo(`${this.path.administration.root}/${this.path.administration.roles}?id=${history.eventId}&returnUrl=${returnUrl}`);
          break;
        case HistoryModuleName.ADVERTISING:
          this.redirectTo(`${this.path.administration.root}/${this.path.administration.publicPageConfig.root}/${this.path.administration.publicPageConfig.advertising.root}?id=${history.eventId}&returnUrl=${returnUrl}`);
          break;
        case HistoryModuleName.INTERNET_GROUPS:
          this.redirectTo(`${this.path.administration.root}/${this.path.administration.internetGroupsConfig}?id=${history.eventId}&returnUrl=${returnUrl}`);
          break;
        default:
          break;
      }
    } else {
      this.toasterService.warning(MESSAGES.dataHasBeenDeleted);
    }
  }

  handleIncomingTruckRedirect(history: HistoryListItem, returnUrl: string): void {
    if (history.action === HistoryModuleActions.MOVED) {
      this.redirectTo(`${this.path.inventory.root}?id=${history.eventId}&returnUrl=${returnUrl}`);
    } else {
      this.redirectTo(`${this.path.transport.root}/${this.path.transport.incomingTruckBoard.root}?id=${history.eventId}&returnUrl=${returnUrl}`)
    }
  }

  redirectTo(url: string): void {
    this.commonSharedService.setBlockUI$(true);
    this.router.navigateByUrl(url);
  }

  isIncomingTruckToInventory(history: HistoryListItem): boolean {
    if ((this.moduleName === HistoryModuleName.INCOMING_TRUCK || this.moduleName === HistoryModuleName.INVENTORY) && history.action === HistoryModuleActions.MOVED) {
      return true;
    }
    return false;
  }
}
